// ignore_for_file: use_build_context_synchronously

import 'package:bottle_king_mobile/core/core.dart';

class ResendOtpOptionDialog extends StatefulWidget {
  const ResendOtpOptionDialog({
    super.key,
  });

  @override
  State<ResendOtpOptionDialog> createState() => _ResendOtpOptionDialogState();
}

class _ResendOtpOptionDialogState extends State<ResendOtpOptionDialog> {
  final passwordC = TextEditingController();
  final focusNode = FocusNode();

  @override
  dispose() {
    passwordC.dispose();
    focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: EdgeInsets.symmetric(
        horizontal: Sizer.width(50),
      ),
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(24),
        ),
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const YBox(30),
            ResendOption(
              svgPath: AppSvgs.whatsapp,
              text: "Resend via WhatsApp",
              onTap: () {},
            ),
            const YBox(16),
            ResendOption(
              svgPath: AppSvgs.sms,
              text: "Resend via SMS",
              onTap: () {},
            ),
            const YBox(30),
          ],
        ),
      ),
    );
  }
}

class ResendOption extends StatelessWidget {
  const ResendOption({
    super.key,
    required this.svgPath,
    required this.text,
    required this.onTap,
  });

  final String svgPath;
  final String text;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Row(
        children: [
          SvgPicture.asset(svgPath, height: Sizer.height(20)),
          const XBox(12),
          Text(
            text,
            style: AppTypography.text16,
          ),
        ],
      ),
    );
  }
}
