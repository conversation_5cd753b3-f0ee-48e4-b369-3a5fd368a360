import 'package:bottle_king_mobile/core/core.dart';

class CartIncrement extends StatelessWidget {
  const CartIncrement({
    super.key,
    required this.value,
    this.vPadding,
    this.onCrement,
    this.onDecrement,
    this.onQuantityChanged,
    this.height,
    this.width,
    required this.controller,
  });

  final int value;
  final VoidCallback? onCrement;
  final VoidCallback? onDecrement;
  final Function(int)? onQuantityChanged;
  final double? vPadding;
  final double? height;
  final double? width;
  final TextEditingController controller;

  void _handleQuantityInput(String value) {
    if (onQuantityChanged == null) return;

    // Handle empty input
    if (value.trim().isEmpty) {
      return;
    }

    // Parse the input and validate
    final parsedValue = int.tryParse(value.trim());
    if (parsedValue == null) {
      // Invalid input - reset to current value
      controller.text = this.value.toString();
      return;
    }

    // Validate minimum quantity (must be at least 1)
    if (parsedValue < 1) {
      controller.text = "1";
      onQuantityChanged!(1);
      return;
    }

    // Valid input - call the callback
    onQuantityChanged!(parsedValue);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: Sizer.height(height ?? 30),
      width: Sizer.width(width ?? 100),
      padding: EdgeInsets.symmetric(
        horizontal: Sizer.width(4),
        vertical: Sizer.height(vPadding ?? 0),
      ),
      decoration: BoxDecoration(
        border: Border.all(
          color: AppColors.blackBD,
        ),
      ),
      child: Row(
        children: [
          InkWell(
              onTap: onDecrement,
              child: Icon(
                Icons.remove,
                size: Sizer.height(20),
                color: AppColors.black70,
              )),
          const XBox(6),
          Expanded(
            child: TextField(
              controller: controller,
              textAlign: TextAlign.center,
              keyboardType: TextInputType.number,
              style: AppTypography.text16.copyWith(
                fontWeight: FontWeight.w500,
              ),
              decoration: const InputDecoration(
                isCollapsed: true,
                isDense: true,
                border: InputBorder.none,
                contentPadding: EdgeInsets.zero,
              ),
              onChanged: (value) {
                _handleQuantityInput(value);
              },
              onSubmitted: (value) {
                _handleQuantityInput(value);
              },
            ),
          ),
          const XBox(6),
          InkWell(
            onTap: onCrement,
            child: Icon(
              Icons.add,
              size: Sizer.height(20),
              color: AppColors.black70,
            ),
          ),
        ],
      ),
    );
  }
}
