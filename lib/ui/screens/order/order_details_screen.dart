import 'package:bottle_king_mobile/core/core.dart';
import 'package:bottle_king_mobile/ui/components/components.dart';

class OrderDetailsScreen extends ConsumerStatefulWidget {
  const OrderDetailsScreen({
    super.key,
    required this.order,
  });

  final OrderModel order;

  @override
  ConsumerState<OrderDetailsScreen> createState() => _OrderDetailsScreenState();
}

class _OrderDetailsScreenState extends ConsumerState<OrderDetailsScreen> {
  int selectedIndex = 0;
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkRatingStatus();
    });
  }

  _checkRatingStatus() async {
    final r = await ref.read(orderViewModel).checkRatingStatus(
          params: RateFeedbackParams(
            orderId: widget.order.orderId ?? "",
          ),
        );

    handleApiResponse(
      response: r,
      onSuccess: () {
        if (r.data["ces"] == true) {
          selectedIndex = 1;
        } else if (r.data["csat"] == true) {
          selectedIndex = 2;
        }
        setState(() {});
      },
    );
  }

  bool get statusIsPending =>
      widget.order.status == DeliveryConst.pending ||
      widget.order.status == DeliveryConst.shipped ||
      widget.order.status == DeliveryConst.delivered;

  bool get statusIsShipped =>
      widget.order.status == DeliveryConst.shipped ||
      widget.order.status == DeliveryConst.delivered;

  bool get statusIsDelivered => widget.order.status == DeliveryConst.delivered;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppbar(
        title: "Order Details",
      ),
      body: ListView(
        padding: EdgeInsets.only(
          top: Sizer.height(10),
          bottom: Sizer.height(200),
        ),
        children: [
          Padding(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(16),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: EdgeInsets.all(Sizer.radius(4)),
                  decoration: const BoxDecoration(
                    color: AppColors.greyF7,
                  ),
                  child: RichText(
                      text: TextSpan(children: [
                    TextSpan(
                      text: "Order ID:  ",
                      style: AppTypography.text14.copyWith(
                        color: AppColors.black70,
                        fontWeight: FontWeight.w500,
                        fontFamily: "GeneralSans",
                      ),
                    ),
                    TextSpan(
                      text: "${widget.order.orderId}",
                      style: AppTypography.text14.copyWith(
                        color: AppColors.primaryBlack,
                        fontWeight: FontWeight.w500,
                        fontFamily: "GeneralSans",
                      ),
                    )
                  ])),
                ),
                const YBox(16),
                Row(
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "Order type:",
                          style: AppTypography.text14.copyWith(
                            color: AppColors.black70,
                            fontWeight: FontWeight.w500,
                            fontFamily: "GeneralSans",
                          ),
                        ),
                        const YBox(4),
                        Text(
                          (widget.order.deliveryType ?? "").capitalize(),
                          style: AppTypography.text14.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                    const Spacer(),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          AppUtils.dayWithSuffixMonthAndYear(
                              widget.order.createdAt ?? DateTime.now()),
                          style: AppTypography.text14.copyWith(
                            color: AppColors.black70,
                            fontWeight: FontWeight.w500,
                            fontFamily: "GeneralSans",
                          ),
                        ),
                        const YBox(4),
                        Text(
                          AppUtils.convertDateTime(
                              widget.order.createdAt ?? DateTime.now()),
                          style: AppTypography.text14.copyWith(
                            color: AppColors.black70,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
          const YBox(16),
          const HLine(),
          const YBox(30),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: Sizer.width(16)),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Your items",
                  style: AppTypography.text14.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const YBox(16),
                const Divider(color: AppColors.greyF7, thickness: 2, height: 1),
                const YBox(16),
                ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  padding: EdgeInsets.zero,
                  itemBuilder: (ctx, i) {
                    final product = widget.order.products?[i];
                    return OrderProductTile(
                      imageUrl: product?.image ?? "",
                      productName: product?.name ?? "",
                      quantity: product?.qty ?? 1,
                      price: product?.price ?? 0,
                    );
                  },
                  separatorBuilder: (_, __) => const YBox(12),
                  itemCount: widget.order.products?.length ?? 0,
                ),
                const YBox(16),
              ],
            ),
          ),
          const HLine(),
          Padding(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(16),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const YBox(12),
                Text(
                  "Order status",
                  style: AppTypography.text16.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const YBox(16),
                // if (statusIsPending)
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        SvgPicture.asset(
                          statusIsPending
                              ? AppSvgs.stepCheck
                              : AppSvgs.stepInactive,
                        ),
                        const YBox(4),
                        SvgPicture.asset(statusIsShipped
                            ? AppSvgs.stepVLine
                            : AppSvgs.vlineInactive),
                        const YBox(4),
                      ],
                    ),
                    const XBox(12),
                    Expanded(
                      child: StepColumnText(
                        isActive: statusIsPending,
                        title: "Order processing",
                        subtitle:
                            "We  are currently putting together your order. Thanks!",
                      ),
                    ),
                  ],
                ),
                // if (statusIsShipped)
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        SvgPicture.asset(statusIsShipped
                            ? AppSvgs.stepCircle
                            : AppSvgs.stepInactive),
                        const YBox(4),
                        SvgPicture.asset(statusIsDelivered
                            ? AppSvgs.stepVLine
                            : AppSvgs.vlineInactive),
                        const YBox(4),
                      ],
                    ),
                    const XBox(12),
                    Expanded(
                      child: StepColumnText(
                        isActive: statusIsShipped,
                        title: "Order shipped",
                        subtitle: "Our rider is on the way to you.",
                      ),
                    ),
                  ],
                ),
                // if (statusIsDelivered)
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const YBox(4),
                        SvgPicture.asset(statusIsDelivered
                            ? AppSvgs.stepCheck
                            : AppSvgs.stepInactive),
                        // const YBox(4),
                        // SvgPicture.asset(AppSvgs.stepVLine),
                      ],
                    ),
                    const XBox(12),
                    Expanded(
                      child: StepColumnText(
                        isActive: statusIsDelivered,
                        title: "Order delivered",
                        subtitle:
                            "Your order has been delivered! Thanks for ordering.",
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const YBox(40),
          const HLine(),
          const YBox(16),
          Padding(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(16),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Delivery address",
                  style: AppTypography.text16.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const YBox(16),
                Row(
                  children: [
                    // Container(
                    //   height: Sizer.height(8),
                    //   width: Sizer.width(8),
                    //   decoration: BoxDecoration(
                    //     color: AppColors.yellow37,
                    //     borderRadius: BorderRadius.circular(10),
                    //   ),
                    // ),
                    Icon(
                      Icons.location_on_outlined,
                      color: AppColors.black70,
                      size: Sizer.radius(20),
                    ),
                    const XBox(8),
                    Expanded(
                      child: Text(
                        widget.order.addressLabel ?? "N/A",
                        style: AppTypography.text14.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const YBox(16),
          const HLine(),
          const YBox(16),
          Padding(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(16),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Rate your experience",
                  style: AppTypography.text16.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const YBox(16),
                Container(
                  padding: EdgeInsets.all(Sizer.height(16)),
                  decoration: const BoxDecoration(
                    color: AppColors.grayFA,
                    // borderRadius: BorderRadius.circular(Sizer.radius(8)),
                    // border: Border.all(width: 1, color: AppColors.grayF0),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: List.generate(AppText.emojis.length, (i) {
                      final emoji = AppText.emojis[i];
                      return InkWell(
                        onTap: () {
                          ModalWrapper.bottomSheet(
                            context: context,
                            widget: RateOrderModal(
                              selectedIndex: i,
                              orderId: widget.order.orderId ?? "",
                            ),
                          );
                        },
                        child: Image.asset(
                          emoji["image"] ?? "",
                          height: Sizer.height(44),
                        ),
                      );
                    }),
                  ),
                ),
              ],
            ),
          ),
          const YBox(16),
          const HLine(),
          const YBox(16),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: Sizer.width(16)),
            child: Column(
              children: [
                // const CheckoutAmountTile(
                //   leftText: "Subtotal",
                //   rightText: "N18,000",
                // ),
                const YBox(20),
                CheckoutAmountTile(
                  leftText: "Delivery fee",
                  rightText:
                      '${AppUtils.nairaSymbol}${AppUtils.formatNumber(number: widget.order.fare ?? 0)}',
                ),
                const YBox(20),
                CheckoutAmountTile(
                  leftText: "Order total",
                  rightText:
                      '${AppUtils.nairaSymbol}${AppUtils.formatNumber(number: widget.order.payment?.amount ?? 0)}',
                  color: AppColors.primaryBlack,
                ),
                const YBox(40),
                CustomBtn.solid(
                  onTap: () async {
                    final r = await ref
                        .read(cartVm.notifier)
                        .reorder(orderId: widget.order.orderId ?? "");

                    handleApiResponse(
                        response: r,
                        onSuccess: () {
                          Navigator.pushNamedAndRemoveUntil(
                            context,
                            RoutePath.bottomNavScreen,
                            (r) => false,
                            arguments: DashArg(index: 3),
                          );
                        });
                  },
                  online: true,
                  isOutline: true,
                  isLoading: ref.watch(cartVm).busy(reorderState),
                  outlineColor: AppColors.blackBD,
                  textColor: AppColors.primaryBlack,
                  text: "Order again",
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}

class StepColumnText extends StatelessWidget {
  const StepColumnText({
    super.key,
    required this.title,
    required this.subtitle,
    this.isActive = false,
  });

  final String title;
  final String subtitle;
  final bool isActive;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: AppTypography.text14.copyWith(
            fontWeight: FontWeight.w500,
            color: isActive ? AppColors.primaryBlack : AppColors.blackBD,
          ),
        ),
        Text(
          subtitle,
          style: AppTypography.text14.copyWith(
            color: isActive ? AppColors.black70 : AppColors.blackBD,
          ),
        ),
      ],
    );
  }
}
