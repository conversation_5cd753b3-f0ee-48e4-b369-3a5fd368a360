import 'package:bottle_king_mobile/core/core.dart';
import 'package:lottie/lottie.dart';

class SplashScreen extends ConsumerStatefulWidget {
  const SplashScreen({super.key});

  @override
  ConsumerState<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends ConsumerState<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _mainAnimationController;
  late AnimationController _loadingAnimationController;

  late Animation<double> _logoFadeAnimation;
  late Animation<Offset> _logoDropAnimation;
  late Animation<double> _textFadeAnimation;
  late Animation<Offset> _textSlideAnimation;
  late Animation<double> _loadingFadeAnimation;

  @override
  void initState() {
    super.initState();
    final authRef = ref.read(authVm);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      LocationService.getUserLocationDetails(ref: ref);
    });

    _initializeAnimations();
    _startAnimationSequence();

    Future.delayed(const Duration(seconds: 4), () async {
      final userLoaded = await authRef.loadUserFromStorage();
      // final hasSeenOnboarding = await StorageService.getBoolItem(
      //       StorageKey.hasSeenOnboarding,
      //     ) ??
      //     false;
      final nextPage = userLoaded
          ? RoutePath.bottomNavScreen
          // : hasSeenOnboarding
          //     ? RoutePath.bottomNavScreen
          : RoutePath.onboardingScreen;

      if (mounted) {
        Navigator.of(context).pushReplacementNamed(nextPage);
      }
    });
  }

  void _initializeAnimations() {
    // Main animation controller for logo drop and text
    _mainAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2500),
    );

    // Loading animation controller
    _loadingAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    // Logo drop animation - starts from above screen and settles below center
    _logoDropAnimation = Tween<Offset>(
      begin: const Offset(0, -1.5), // Start above screen
      end:
          const Offset(0, 0.4), // End slightly below center for settling effect
    ).animate(CurvedAnimation(
      parent: _mainAnimationController,
      curve: const Interval(0.0, 0.8, curve: Curves.bounceOut),
    ));

    // Logo fade animation - fades in as it drops
    _logoFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _mainAnimationController,
      curve: const Interval(0.0, 0.4, curve: Curves.easeIn),
    ));

    // Text animations - appear after logo settles
    _textFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _mainAnimationController,
      curve: const Interval(0.6, 0.9, curve: Curves.easeOut),
    ));

    _textSlideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _mainAnimationController,
      curve: const Interval(0.6, 0.9, curve: Curves.easeOut),
    ));

    // Loading indicator animation
    _loadingFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _mainAnimationController,
      curve: const Interval(0.8, 1.0, curve: Curves.easeIn),
    ));
  }

  void _startAnimationSequence() {
    _mainAnimationController.forward();

    // Start loading animation after logo settles
    Future.delayed(const Duration(milliseconds: 2000), () {
      if (mounted) {
        _loadingAnimationController.repeat();
      }
    });
  }

  @override
  void dispose() {
    _mainAnimationController.dispose();
    _loadingAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.black,
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Logo with drop-and-bounce animation
                    SlideTransition(
                      position: _logoDropAnimation,
                      child: FadeTransition(
                        opacity: _logoFadeAnimation,
                        child: imageHelper(
                          AppImages.logo,
                          // height: Sizer.height(114),
                          width: Sizer.width(200),
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),

                    // const YBox(40),

                    // // App name with slide and fade animation
                    // SlideTransition(
                    //   position: _textSlideAnimation,
                    //   child: FadeTransition(
                    //     opacity: _textFadeAnimation,
                    //     child: Column(
                    //       children: [
                    //         Lottie.asset('assets/images/wine.json',
                    //             height: Sizer.height(80)),
                    //       ],
                    //     ),
                    //   ),
                    // ),
                  ],
                ),
              ),
            ),

            // Loading indicator at bottom
            FadeTransition(
              opacity: _loadingFadeAnimation,
              child: Padding(
                padding: EdgeInsets.only(bottom: Sizer.height(40)),
                child: Column(
                  children: [
                    Lottie.asset('assets/images/wine.json',
                        height: Sizer.height(60)),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return AnimatedBuilder(
      animation: _loadingAnimationController,
      builder: (context, child) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(3, (index) {
            final delay = index * 0.2;
            final animationValue =
                (_loadingAnimationController.value - delay).clamp(0.0, 1.0);
            final scale = 0.5 +
                (0.5 * (1 - (animationValue - 0.5).abs() * 2).clamp(0.0, 1.0));

            return Container(
              margin: EdgeInsets.symmetric(horizontal: Sizer.width(4)),
              child: Transform.scale(
                scale: scale,
                child: Container(
                  width: Sizer.width(8),
                  height: Sizer.width(8),
                  decoration: BoxDecoration(
                    color: AppColors.yellow37,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.yellow37.withValues(alpha: 0.5),
                        blurRadius: 8,
                        spreadRadius: 1,
                      ),
                    ],
                  ),
                ),
              ),
            );
          }),
        );
      },
    );
  }
}
