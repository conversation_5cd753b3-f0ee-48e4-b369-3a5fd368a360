import 'package:bottle_king_mobile/core/core.dart';

const String getCartState = "getCartState";
const String getWishlistState = "getWishlistState";
const String removeState = "removeState";
const String reorderState = "reorderState";
const String rewardGenerateCouponState = "rewardGenerateCouponState";
const String validateCouponState = "validateCouponState";

class CartVm extends BaseVm {
  CartModel? _cartModel;
  CartProducts? get regularCartProduct => _cartModel?.items?.regularProducts;
  CartModel? _wishList;
  List<CartItem> get wishList => _wishList?.items?.regularProducts?.items ?? [];

  Future<ApiResponse> addToCartOrWishlist({
    required String productId,
    required String variationId,
    String quantity = "1",
    bool isWishList = false,
  }) async {
    printty("addToCartOrWishlist productId: $productId");
    return await performApiCall(
      url: "/cart/add",
      method: apiService.postWithAuth,
      isFormData: false,
      body: {
        "productId": productId,
        "variationId": variationId,
        "quantity": quantity,
        "isWishList": isWishList
      },
      onSuccess: (data) {
        isWishList
            ? getWishlist(showLoader: false)
            : getCart(showLoader: false);
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> updateCartOrWishlist({
    required String itemId,
    int quantity = 1,
    bool isWishList = false,
  }) async {
    printty("addToCartOrWishlist itemId: $itemId");
    return await performApiCall(
      url: "/cart/update",
      method: apiService.putWithAuth,
      isFormData: true,
      body: {
        "itemId": itemId,
        "quantity": quantity,
        "isWishList": isWishList,
      },
      onSuccess: (data) {
        _cartModel = cartModelFromJson(json.encode(data["data"]));
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> getCart({
    bool showLoader = true,
  }) async {
    return await performApiCall(
      url: "/cart?wishlist=false",
      method: apiService.getWithAuth,
      busyObjectName: showLoader ? getCartState : "",
      onSuccess: (data) {
        _cartModel = cartModelFromJson(json.encode(data["data"]));
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> getWishlist({
    bool showLoader = true,
  }) async {
    return await performApiCall(
      url: "/cart?wishlist=true",
      method: apiService.getWithAuth,
      busyObjectName: showLoader ? getWishlistState : "",
      onSuccess: (data) {
        _wishList = cartModelFromJson(json.encode(data["data"]));
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> removeCartOrWishlist({
    required String itemId,
    bool isWishList = false,
  }) async {
    printty("removeCartOrWishlist productId: $itemId");
    return await performApiCall(
      url: "/cart/remove/$itemId?isWishList=$isWishList",
      method: apiService.deleteWithAuth,
      busyObjectName: removeState,
      onSuccess: (data) {
        isWishList
            ? getWishlist(showLoader: false)
            : getCart(showLoader: false);
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> clearCartOrWishlist({
    bool isWishList = false,
  }) async {
    return await performApiCall(
      url: "/cart/clear?isWishList=$isWishList",
      method: apiService.deleteWithAuth,
      busyObjectName: removeState,
      onSuccess: (data) {
        isWishList
            ? getWishlist(showLoader: false)
            : getCart(showLoader: false);
        return apiResponse;
      },
    );
  }

  Future<ApiResponse<Coupon>> rewardGenerateCoupon(
    String customerId,
  ) async {
    return await performApiCall<Coupon>(
      url: "/reward/generate-coupon",
      method: apiService.postWithAuth,
      busyObjectName: rewardGenerateCouponState,
      body: {"customerId": customerId},
      onSuccess: (data) {
        // getCart(showLoader: false);
        return ApiResponse<Coupon>(
          success: true,
          data: Coupon.fromJson(data["data"]["coupon"]),
        );
      },
    );
  }

  Future<ApiResponse> validateCoupon(
    String code,
  ) async {
    return await performApiCall(
      url: "/coupon/validate?code=$code",
      method: apiService.getWithAuth,
      busyObjectName: validateCouponState,
      onSuccess: (data) {
        return ApiResponse(
          success: true,
          data: data["data"]?["value"],
        );
      },
    );
  }

  Future<ApiResponse> reorder({
    required String orderId,
  }) async {
    return await performApiCall(
      url: "/order/reorder/$orderId",
      method: apiService.postWithAuth,
      busyObjectName: reorderState,
      onSuccess: (data) {
        getCart(showLoader: false);
        return apiResponse;
      },
    );
  }

  /// Optimistically removes an item from the cart and attempts to sync with backend
  /// If the backend call fails, the item is restored and an error is shown
  Future<ApiResponse> removeCartItemOptimistically({
    required String itemId,
    bool isWishList = false,
  }) async {
    // Store the original cart state for potential rollback
    final originalCartModel = _cartModel;
    final originalWishList = _wishList;

    // Find the item to remove
    final targetList = isWishList ? _wishList : _cartModel;
    final itemToRemove = targetList?.items?.regularProducts?.items
        ?.firstWhere((item) => item.id == itemId, orElse: () => CartItem());

    if (itemToRemove?.id == null) {
      return ApiResponse(
        success: false,
        message: "Item not found in cart",
      );
    }

    // Optimistically update the UI by removing the item
    _removeItemFromLocalState(itemId, isWishList);

    // Notify listeners to update UI immediately
    notifyListeners();

    try {
      // Attempt to remove from backend
      final response = await performApiCall(
        url: "/cart/remove/$itemId?isWishList=$isWishList",
        method: apiService.deleteWithAuth,
        busyObjectName:
            "", // Don't show loading since we're doing optimistic update
        onSuccess: (data) {
          // Backend success - refresh cart to ensure consistency
          isWishList
              ? getWishlist(showLoader: false)
              : getCart(showLoader: false);
          return apiResponse;
        },
      );

      if (!response.success) {
        // Backend failed - restore original state
        _restoreOriginalState(originalCartModel, originalWishList, isWishList);
        notifyListeners();

        // Show error message
        FlushBarToast.fLSnackBar(
          snackBarType: SnackBarType.warning,
          message:
              response.message ?? "Failed to remove item. Please try again.",
        );
      }

      return response;
    } catch (e) {
      // Exception occurred - restore original state
      _restoreOriginalState(originalCartModel, originalWishList, isWishList);
      notifyListeners();

      // Show error message
      FlushBarToast.fLSnackBar(
        snackBarType: SnackBarType.warning,
        message: "Failed to remove item. Please check your connection.",
      );

      return ApiResponse(
        success: false,
        message: e.toString(),
      );
    }
  }

  /// Removes an item from local state (optimistic update)
  void _removeItemFromLocalState(String itemId, bool isWishList) {
    if (isWishList && _wishList != null) {
      final updatedItems = _wishList!.items?.regularProducts?.items
              ?.where((item) => item.id != itemId)
              .toList() ??
          [];
      final updatedTotal = _calculateTotal(updatedItems);

      _wishList = _wishList!.copyWith(
        items: _wishList!.items?.copyWith(
          regularProducts: _wishList!.items?.regularProducts?.copyWith(
            items: updatedItems,
            total: updatedTotal,
          ),
        ),
      );
    } else if (!isWishList && _cartModel != null) {
      final updatedItems = _cartModel!.items?.regularProducts?.items
              ?.where((item) => item.id != itemId)
              .toList() ??
          [];
      final updatedTotal = _calculateTotal(updatedItems);

      _cartModel = _cartModel!.copyWith(
        items: _cartModel!.items?.copyWith(
          regularProducts: _cartModel!.items?.regularProducts?.copyWith(
            items: updatedItems,
            total: updatedTotal,
          ),
        ),
      );
    }
  }

  /// Restores the original cart state in case of backend failure
  void _restoreOriginalState(
      CartModel? originalCart, CartModel? originalWishList, bool isWishList) {
    if (isWishList) {
      _wishList = originalWishList;
    } else {
      _cartModel = originalCart;
    }
  }

  /// Calculates the total price for a list of cart items
  int _calculateTotal(List<CartItem> items) {
    return items.fold<int>(0, (total, item) {
      final itemPrice = item.price ?? 0;
      final itemQuantity = item.quantity ?? 1;
      return total + (itemPrice * itemQuantity);
    });
  }
}

final cartVm = ChangeNotifierProvider((ref) {
  return CartVm();
});
