import 'package:bottle_king_mobile/core/core.dart';

const String rateState = "rateState";
const String rateEligibiltyState = "rateEligibiltyState";

class OrderVm extends BaseVm {
  List<OrderModel> _orderList = [];

  // Get orders where payment is not null
  List<OrderModel> get orderList =>
      _orderList.where((element) => element.payment != null).toList();

  Future<ApiResponse> getOrdersList({
    String? status,
  }) async {
    UriBuilder uriBuilder = UriBuilder("/order")
      ..addQueryParameterIfNotEmpty("status", status ?? "");
    return await performApiCall(
      url: uriBuilder.build().toString(),
      method: apiService.getWithAuth,
      onSuccess: (data) {
        _orderList = orderModelFromJson(json.encode(data["data"]));
        return apiResponse;
      },
    );
  }

  Future<ApiResponse<OrderModel>> viewSingleOrder({
    required String ref,
    String? orderId,
  }) async {
    UriBuilder uriBuilder = UriBuilder("/order/get-order")
      ..addQueryParameterIfNotEmpty("ref", ref)
      ..addQueryParameterIfNotEmpty("orderId", orderId ?? "");
    return await performApiCall<OrderModel>(
      url: uriBuilder.build().toString(),
      method: apiService.getWithAuth,
      onSuccess: (data) {
        return ApiResponse<OrderModel>(
          success: true,
          data: OrderModel.fromJson(data["data"]),
        );
      },
    );
  }

  Future<ApiResponse> initiateCheckout({
    required Map<String, dynamic> args,
  }) async {
    args.removeWhere((k, v) => v == null || v == "");
    return await performApiCall(
      url: "/checkout/initiate-checkout",
      method: apiService.postWithAuth,
      body: args,
      onSuccess: (data) {
        printty("initiateCheckout data: $data");
        return apiResponse;
      },
    );
  }

  // "deliveryType": "drop-off",
  //   "cartId": "{{cartId}}"

  Future<ApiResponse> initiateTransaction({
    required String email,
    required num amount,
    required String deliveryType,
    required String cartId,
  }) async {
    return await performApiCall(
      url: "/checkout/initiate-transaction",
      method: apiService.postWithAuth,
      body: {
        "email": email,
        "amount": amount,
        "deliveryType": deliveryType,
        "cartId": cartId,
      },
      onSuccess: (data) {
        printty("initiateCheckout data: $data");
        return ApiResponse(success: true, data: data["data"]);
      },
    );
  }

  Future<ApiResponse> finalizeCheckout({
    required Map<String, dynamic> args,
  }) async {
    args.removeWhere((k, v) => v == null || v == "");
    return await performApiCall(
      url: "/cart/checkout?isWishList=false",
      method: apiService.postWithAuth,
      body: args,
      onSuccess: (data) {
        printty("initiateCheckout data: $data");
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> cesRateFeedback(
      {required RateFeedbackParams params}) async {
    final payload = params.toJson();
    payload.removeWhere((k, v) => v == null || v == "");
    return await performApiCall(
      url: "/feedback/ces",
      method: apiService.postWithAuth,
      busyObjectName: rateState,
      body: payload,
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> csatRateFeedback(
      {required RateFeedbackParams params}) async {
    final payload = params.toJson();
    payload.removeWhere((k, v) => v == null || v == "");
    return await performApiCall(
      url: "/feedback/csat",
      method: apiService.postWithAuth,
      busyObjectName: rateState,
      body: payload,
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> checkRatingStatus(
      {required RateFeedbackParams params}) async {
    final payload = params.toJson();
    payload.removeWhere((k, v) => k == "orderId" || v == null || v == "");
    return await performApiCall(
      url: "/feedback/status/${params.orderId}",
      method: apiService.getWithAuth,
      body: payload,
      busyObjectName: rateEligibiltyState,
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> checkCsatEligibility(
      {required RateFeedbackParams params}) async {
    final payload = params.toJson();
    payload.removeWhere((k, v) => k == "orderId" || v == null || v == "");
    return await performApiCall(
      url: "/feedback/csat/eligibility/${params.orderId}",
      method: apiService.getWithAuth,
      body: payload,
      busyObjectName: rateEligibiltyState,
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }
}

final orderViewModel = ChangeNotifierProvider((ref) {
  return OrderVm();
});
